apiVersion: v1
kind: Secret
metadata:
  name: ai-nest-backend-secrets
  labels:
    app: ai-nest-backend
    app.kubernetes.io/name: ai-nest-backend
    app.kubernetes.io/component: secrets
    app.kubernetes.io/part-of: ai-nest-backend
    app.kubernetes.io/version: "0928cf15"
    app.kubernetes.io/managed-by: argocd
    environment: dev
type: Opaque
data:
  # Development Environment Secrets
  # These placeholders will be replaced with base64-encoded values from secrets_encoded

  

  

  
  DATABASE_URL: ****************************************************************************************************************************************************************************************************************
  

  # Essential Authentication Secrets
  JWT_SECRET: c3VwZXJzZWNyZXRrZXk=
  

  # Database Credentials (Development)
  DB_USER: bmVzdF9kZXZfdXNlcg==
  DB_PASSWORD: QVZOU19xczczanFuY3FkZ01ZdFpCemIz
  DB_HOST: cHJpdmF0ZS1kYmFhcy1kYi0xMDMyOTMyOC1kby11c2VyLTIzODE1NzQyLTAubS5kYi5vbmRpZ2l0YWxvY2Vhbi5jb20=
  DB_PORT: MjUwNjA=
  DB_NAME: bmVzdF9kZXZfZGI=
  DB_SSL_MODE: cmVxdWlyZQ==

  # SMTP Configuration (Development)
  SMTP_USER: ****************************************
  SMTP_PASS: ZnFhY3RlaGFmbXpsbHR6eg==

  # OAuth2 Configuration (Development)
  GOOGLE_CLIENT_ID: MTA3Mzk4MTg2NDUzOC0zdWlpazcyb2hzZnIyb3Vpb3JvcjNmbTFqcWM0OTNvcy5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbQ==
  GOOGLE_CLIENT_SECRET: R09DU1BYLTcyRjBONEg5aGlMSVk1U3o1Z3pCczI5OEFBYlQ=

  

  # Development-specific secrets
  # DEBUG_MODE: DYNAMIC_DEBUG_MODE_B64
  # LOG_LEVEL: DYNAMIC_LOG_LEVEL_B64
