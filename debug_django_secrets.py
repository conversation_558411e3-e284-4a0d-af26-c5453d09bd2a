#!/usr/bin/env python3
"""
Debug script to test Django backend secret substitution
"""

import json
import base64
import tempfile
import sys
import os
from pathlib import Path

# Add scripts directory to path
sys.path.insert(0, str(Path(__file__).parent / 'scripts'))

from process_payload import parse_secrets, create_placeholder_mapping, process_handlebars_conditionals

def create_test_payload():
    """Create a test payload similar to the one described in the issue"""
    
    # Create the secrets JSON as described in the issue
    secrets_json = {
        "SESSION_SECRET": "test-session-secret-value",
        "RATE_LIMIT_WINDOW_MS": "900000",
        "RATE_LIMIT_MAX_REQUESTS": "100",
        "PASSWORD_RESET_TOKEN_EXPIRY": "3600",
        "EMAIL_VERIFICATION_TOKEN_EXPIRY": "86400",
        "DB_NAME": "django_test_db",
        "DB_USER": "django_user",
        "DB_PASSWORD": "django_password",
        "DB_HOST": "localhost",
        "DB_PORT": "5432",
        "SMTP_PASS": "smtp_password",
        "SMTP_USER": "<EMAIL>",
        "JWT_SECRET": "jwt-secret-key",
        "JWT_EXPIRES_IN": "3600",
        "JWT_REFRESH_EXPIRES_IN": "86400",
        "GOOGLE_CLIENT_ID": "google-client-id",
        "GOOGLE_CLIENT_SECRET": "google-client-secret"
    }
    
    # Encode secrets to base64
    secrets_encoded = base64.b64encode(json.dumps(secrets_json).encode('utf-8')).decode('utf-8')
    
    payload = {
        "project_id": "ai-django-backend-test",
        "application_type": "django-backend",
        "environment": "dev",
        "container_port": 8000,
        "docker_image": "registry.digitalocean.com/doks-registry/ai-django-backend",
        "docker_tag": "v1.0.0",
        "source_repo": "ChidhagniConsulting/ai-django-backend",
        "source_branch": "main",
        "commit_sha": "abc123def456",
        "secrets_encoded": secrets_encoded
    }
    
    return payload, secrets_json

def test_secret_parsing():
    """Test secret parsing functionality"""
    print("=" * 60)
    print("TESTING SECRET PARSING")
    print("=" * 60)
    
    payload, expected_secrets = create_test_payload()
    
    # Test parsing secrets
    parsed_secrets = parse_secrets(payload['secrets_encoded'])
    
    print(f"Expected secrets count: {len(expected_secrets)}")
    print(f"Parsed secrets count: {len(parsed_secrets)}")
    print(f"Expected keys: {sorted(expected_secrets.keys())}")
    print(f"Parsed keys: {sorted(parsed_secrets.keys())}")
    
    # Check if all expected secrets are parsed
    missing_secrets = set(expected_secrets.keys()) - set(parsed_secrets.keys())
    if missing_secrets:
        print(f"❌ Missing secrets: {missing_secrets}")
        return False
    else:
        print("✅ All secrets parsed correctly")
        return True

def test_placeholder_mapping():
    """Test placeholder mapping creation"""
    print("\n" + "=" * 60)
    print("TESTING PLACEHOLDER MAPPING")
    print("=" * 60)
    
    payload, expected_secrets = create_test_payload()
    parsed_secrets = parse_secrets(payload['secrets_encoded'])
    
    # Create placeholder mapping
    mapping = create_placeholder_mapping(payload, parsed_secrets)
    
    print(f"Total mapping entries: {len(mapping)}")
    
    # Check for DYNAMIC_*_B64 placeholders
    dynamic_placeholders = [k for k in mapping.keys() if k.startswith('DYNAMIC_') and k.endswith('_B64')]
    print(f"DYNAMIC_*_B64 placeholders: {len(dynamic_placeholders)}")
    
    # Print all dynamic placeholders
    for placeholder in sorted(dynamic_placeholders):
        value_length = len(mapping[placeholder]) if mapping[placeholder] else 0
        print(f"  {placeholder}: {value_length} chars")
    
    # Check if APPLICATION_TYPE is in mapping
    if 'APPLICATION_TYPE' in mapping:
        print(f"✅ APPLICATION_TYPE: {mapping['APPLICATION_TYPE']}")
    else:
        print("❌ APPLICATION_TYPE missing from mapping")
        return False
    
    # Check if all expected Django secrets have DYNAMIC_*_B64 placeholders
    expected_django_secrets = [
        'SESSION_SECRET', 'RATE_LIMIT_WINDOW_MS', 'RATE_LIMIT_MAX_REQUESTS',
        'PASSWORD_RESET_TOKEN_EXPIRY', 'EMAIL_VERIFICATION_TOKEN_EXPIRY',
        'JWT_SECRET', 'JWT_EXPIRES_IN', 'JWT_REFRESH_EXPIRES_IN'
    ]
    
    missing_dynamic = []
    for secret in expected_django_secrets:
        dynamic_key = f"DYNAMIC_{secret}_B64"
        if dynamic_key not in mapping:
            missing_dynamic.append(dynamic_key)
    
    if missing_dynamic:
        print(f"❌ Missing DYNAMIC placeholders: {missing_dynamic}")
        return False
    else:
        print("✅ All expected DYNAMIC placeholders present")
        return True

def test_handlebars_processing():
    """Test handlebars conditional processing"""
    print("\n" + "=" * 60)
    print("TESTING HANDLEBARS PROCESSING")
    print("=" * 60)
    
    payload, expected_secrets = create_test_payload()
    parsed_secrets = parse_secrets(payload['secrets_encoded'])
    mapping = create_placeholder_mapping(payload, parsed_secrets)
    
    # Read the base template
    template_path = Path("manifests/overlays/dev/secret.yaml")
    if not template_path.exists():
        print(f"❌ Template file not found: {template_path}")
        return False
    
    with open(template_path, 'r', encoding='utf-8') as f:
        template_content = f.read()
    
    print(f"Template file: {template_path}")
    print(f"Template length: {len(template_content)} chars")
    
    # Process handlebars conditionals
    processed_content = process_handlebars_conditionals(template_content, mapping)
    
    print(f"Processed content length: {len(processed_content)} chars")
    
    # Check if Django-specific sections are included
    django_secrets = ['SESSION_SECRET', 'RATE_LIMIT_WINDOW_MS', 'RATE_LIMIT_MAX_REQUESTS']
    django_sections_found = 0
    
    for secret in django_secrets:
        if f"DYNAMIC_{secret}_B64" in processed_content:
            django_sections_found += 1
    
    print(f"Django-specific secrets found in processed content: {django_sections_found}/{len(django_secrets)}")
    
    # Check if Spring Boot sections are excluded
    if "SPRING_DATASOURCE_URL" in processed_content:
        print("❌ Spring Boot sections should be excluded for Django backend")
        return False
    else:
        print("✅ Spring Boot sections correctly excluded")
    
    # Check if handlebars conditionals are removed
    if "{{#eq APPLICATION_TYPE" in processed_content:
        print("❌ Handlebars conditionals not fully processed")
        return False
    else:
        print("✅ Handlebars conditionals processed correctly")
    
    return True

def main():
    """Run all tests"""
    print("Django Backend Secret Substitution Debug")
    print("=" * 60)
    
    tests = [
        ("Secret Parsing", test_secret_parsing),
        ("Placeholder Mapping", test_placeholder_mapping),
        ("Handlebars Processing", test_handlebars_processing)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results[test_name] = False
    
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    print(f"\nOverall: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
