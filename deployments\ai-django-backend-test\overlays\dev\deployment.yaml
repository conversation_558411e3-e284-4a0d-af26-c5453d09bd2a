apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-django-backend-test
  labels:
    app: ai-django-backend-test
    app.kubernetes.io/name: ai-django-backend-test
    app.kubernetes.io/component: django-backend
    app.kubernetes.io/part-of: ai-django-backend-test
    app.kubernetes.io/version: "8c019091"
    app.kubernetes.io/managed-by: argocd
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-django-backend-test
  template:
    metadata:
      labels:
        app: ai-django-backend-test
        app.kubernetes.io/name: ai-django-backend-test
        app.kubernetes.io/component: django-backend
        app.kubernetes.io/part-of: ai-django-backend-test
        app.kubernetes.io/version: "8c019091"
    spec:
      containers:
      - name: ai-django-backend-test
        image: registry.digitalocean.com/doks-registry/ai-django-backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        envFrom:
        - configMapRef:
            name: ai-django-backend-test-config
        env:
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-test-secrets
              key: JWT_SECRET
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-test-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-test-secrets
              key: DB_PASSWORD
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-test-secrets
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-test-secrets
              key: DB_PORT
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-test-secrets
              key: DB_NAME
        - name: DB_SSL_MODE
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-test-secrets
              key: DB_SSL_MODE
        
        
        # Django specific database configuration
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-test-secrets
              key: DATABASE_URL
        - name: DJANGO_SETTINGS_MODULE
          valueFrom:
            configMapKeyRef:
              name: ai-django-backend-test-config
              key: DJANGO_SETTINGS_MODULE
        
        
        - name: SMTP_USER
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-test-secrets
              key: SMTP_USER
        - name: SMTP_PASS
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-test-secrets
              key: SMTP_PASS
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-test-secrets
              key: GOOGLE_CLIENT_ID
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-test-secrets
              key: GOOGLE_CLIENT_SECRET
        
        
        # Django Health Checks (Development - faster startup)
        livenessProbe:
          httpGet:
            path: /health/
            port: 8080
          initialDelaySeconds: 45
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health/
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
        
        
        
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
